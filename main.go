package main

import (
	"bufio"
	"encoding/json"
	"os"
)

type ColStruct struct {
	Col []int `json:"Col"`
}

type RoundQueueItem struct {
	PlateSymbolClient []ColStruct `json:"PlateSymbolClient"`
}

type Data struct {
	RoundQueue []RoundQueueItem `json:"RoundQueue"`
}

type Root struct {
	Data Data `json:"data"`
}

func main() {
	jsonStr := `{"data":{"AckQueue":[{"PlateSymbol":[{"Col":[0,5,0]},{"Col":[5,0,3]},{"Col":[5,0,1]}],"AwardTypeFlag":2,"PlateWin":30,"SymbolWin":30,"PoolDetail":[{"Detail":[0,0,0]},{"Detail":[0,0,0]},{"Detail":[0,0,0]}],"Mult":1,"Pool":[20,20,20],"ShowDetail":[{"Detail":[20,0,0]},{"Detail":[0,20,0]},{"Detail":[0,0,20]}],"Plus":10,"FreeRemainRound":10,"FreeTotalRound":50},{"PlateSymbol":[{"Col":[0,4,0]},{"Col":[1,0,2]},{"Col":[0,4,0]}],"AwardTypeFlag":4,"PoolDetail":[{"Detail":[0,0,0]},{"Detail":[0,0,0]},{"Detail":[0,0,0]}],"Mult":1,"Pool":[20,20,20],"ShowDetail":[{"Detail":[0,0,0]},{"Detail":[0,0,0]},{"Detail":[0,0,0]}],"FreeRemainRound":9,"FreeTotalRound":50},{"PlateSymbol":[{"Col":[0,1,0]},{"Col":[0,5,0]},{"Col":[0,3,0]}],"AwardTypeFlag":36,"PlateWin":20,"PoolWin":20,"PoolDetail":[{"Detail":[0,0,0]},{"Detail":[0,20,0]},{"Detail":[0,0,0]}],"Mult":1,"Pool":[20,30,20],"ShowDetail":[{"Detail":[0,0,0]},{"Detail":[0,30,0]},{"Detail":[0,0,0]}],"FreeRemainRound":8,"FreeTotalRound":50},{"PlateSymbol":[{"Col":[1,0,7]},{"Col":[2,0,2]},{"Col":[3,0,1]}],"AwardTypeFlag":4,"PoolDetail":[{"Detail":[0,0,0]},{"Detail":[0,0,0]},{"Detail":[0,0,0]}],"Mult":1,"Pool":[20,30,20],"ShowDetail":[{"Detail":[0,0,0]},{"Detail":[0,0,0]},{"Detail":[0,0,0]}],"FreeRemainRound":7,"FreeTotalRound":50},{"PlateSymbol":[{"Col":[0,1,0]},{"Col":[2,0,1]},{"Col":[0,1,0]}],"AwardTypeFlag":4,"PoolDetail":[{"Detail":[0,0,0]},{"Detail":[0,0,0]},{"Detail":[0,0,0]}],"Mult":1,"Pool":[20,30,20],"ShowDetail":[{"Detail":[0,0,0]},{"Detail":[0,0,0]},{"Detail":[0,0,0]}],"FreeRemainRound":6,"FreeTotalRound":50},{"PlateSymbol":[{"Col":[1,0,7]},{"Col":[0,1,0]},{"Col":[7,0,2]}],"AwardTypeFlag":20,"PoolDetail":[{"Detail":[0,0,0]},{"Detail":[0,0,0]},{"Detail":[0,0,0]}],"Mult":1,"Pool":[20,30,20],"ShowDetail":[{"Detail":[0,0,0]},{"Detail":[0,0,0]},{"Detail":[0,0,0]}],"Plus":5,"FreeRemainRound":10,"FreeTotalRound":55},{"PlateSymbol":[{"Col":[4,0,2]},{"Col":[1,0,1]},{"Col":[5,0,4]}],"AwardTypeFlag":36,"PlateWin":20,"PoolWin":20,"PoolDetail":[{"Detail":[0,0,0]},{"Detail":[0,0,0]},{"Detail":[0,0,20]}],"Mult":1,"Pool":[20,30,30],"ShowDetail":[{"Detail":[0,0,0]},{"Detail":[0,0,0]},{"Detail":[0,0,30]}],"FreeRemainRound":9,"FreeTotalRound":55},{"PlateSymbol":[{"Col":[0,1,0]},{"Col":[0,2,0]},{"Col":[1,0,5]}],"AwardTypeFlag":36,"PlateWin":30,"PoolWin":30,"PoolDetail":[{"Detail":[0,0,0]},{"Detail":[0,0,0]},{"Detail":[0,0,30]}],"Mult":1,"Pool":[20,30,50],"ShowDetail":[{"Detail":[0,0,0]},{"Detail":[0,0,0]},{"Detail":[0,0,50]}],"FreeRemainRound":8,"FreeTotalRound":55},{"PlateSymbol":[{"Col":[0,3,0]},{"Col":[0,1,0]},{"Col":[0,3,0]}],"AwardTypeFlag":5,"PlateWin":10,"LineWin":10,"PoolDetail":[{"Detail":[0,0,0]},{"Detail":[0,0,0]},{"Detail":[0,0,0]}],"Mult":1,"Pool":[20,30,50],"ShowDetail":[{"Detail":[0,0,0]},{"Detail":[0,0,0]},{"Detail":[0,0,0]}],"Light":1,"FreeRemainRound":7,"FreeTotalRound":55},{"PlateSymbol":[{"Col":[0,2,0]},{"Col":[2,0,7]},{"Col":[11,0,2]}],"AwardTypeFlag":4,"PoolDetail":[{"Detail":[0,0,0]},{"Detail":[0,0,0]},{"Detail":[0,0,0]}],"Mult":1,"Pool":[20,30,50],"ShowDetail":[{"Detail":[0,0,0]},{"Detail":[0,0,0]},{"Detail":[0,0,0]}],"FreeRemainRound":6,"FreeTotalRound":55},{"PlateSymbol":[{"Col":[0,1,0]},{"Col":[0,1,0]},{"Col":[0,3,0]}],"AwardTypeFlag":5,"PlateWin":10,"LineWin":10,"PoolDetail":[{"Detail":[0,0,0]},{"Detail":[0,0,0]},{"Detail":[0,0,0]}],"Mult":1,"Pool":[20,30,50],"ShowDetail":[{"Detail":[0,0,0]},{"Detail":[0,0,0]},{"Detail":[0,0,0]}],"Light":1,"FreeRemainRound":5,"FreeTotalRound":55},{"PlateSymbol":[{"Col":[0,8,0]},{"Col":[0,1,0]},{"Col":[0,3,0]}],"AwardTypeFlag":5,"PlateWin":20,"LineWin":20,"PoolDetail":[{"Detail":[0,0,0]},{"Detail":[0,0,0]},{"Detail":[0,0,0]}],"Mult":2,"Pool":[20,30,50],"ShowDetail":[{"Detail":[0,0,0]},{"Detail":[0,0,0]},{"Detail":[0,0,0]}],"Light":1,"FreeRemainRound":4,"FreeTotalRound":55},{"PlateSymbol":[{"Col":[2,0,2]},{"Col":[0,4,0]},{"Col":[0,1,0]}],"AwardTypeFlag":4,"PoolDetail":[{"Detail":[0,0,0]},{"Detail":[0,0,0]},{"Detail":[0,0,0]}],"Mult":1,"Pool":[20,30,50],"ShowDetail":[{"Detail":[0,0,0]},{"Detail":[0,0,0]},{"Detail":[0,0,0]}],"FreeRemainRound":3,"FreeTotalRound":55},{"PlateSymbol":[{"Col":[1,0,1]},{"Col":[0,7,0]},{"Col":[3,0,11]}],"AwardTypeFlag":4,"PoolDetail":[{"Detail":[0,0,0]},{"Detail":[0,0,0]},{"Detail":[0,0,0]}],"Mult":1,"Pool":[20,30,50],"ShowDetail":[{"Detail":[0,0,0]},{"Detail":[0,0,0]},{"Detail":[0,0,0]}],"FreeRemainRound":2,"FreeTotalRound":55},{"PlateSymbol":[{"Col":[0,3,0]},{"Col":[0,8,0]},{"Col":[0,1,0]}],"AwardTypeFlag":5,"PlateWin":20,"LineWin":20,"PoolDetail":[{"Detail":[0,0,0]},{"Detail":[0,0,0]},{"Detail":[0,0,0]}],"Mult":2,"Pool":[20,30,50],"ShowDetail":[{"Detail":[0,0,0]},{"Detail":[0,0,0]},{"Detail":[0,0,0]}],"Light":1,"FreeRemainRound":1,"FreeTotalRound":55},{"PlateSymbol":[{"Col":[0,1,0]},{"Col":[0,2,0]},{"Col":[2,0,2]}],"AwardTypeFlag":12,"PoolDetail":[{"Detail":[0,0,0]},{"Detail":[0,0,0]},{"Detail":[0,0,0]}],"Mult":1,"Pool":[20,30,50],"ShowDetail":[{"Detail":[0,0,0]},{"Detail":[0,0,0]},{"Detail":[0,0,0]}],"FreeTotalRound":55}],"BonusTotalWin":130,"TotalWin":160},"service":{},"totalWin":160,"postMoney":62077.655,"hasspin":true,"BaseBet":10,"RealBet":10,"roundIndexV2":"1779286086496420045","spinReq":{"bet":10,"special":{}},"data_raw":"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"}`

	var root Root
	err := json.Unmarshal([]byte(jsonStr), &root)
	if err != nil {
		panic(err)
	}

	// 提取 PlateSymbolClient 内容为数组
	var allPlateSymbolClient [][][]int

	for _, round := range root.Data.RoundQueue {
		var plateClient [][]int
		for _, col := range round.PlateSymbolClient {
			plateClient = append(plateClient, col.Col)
		}
		allPlateSymbolClient = append(allPlateSymbolClient, plateClient)
	}

	// // 美化打印验证
	// for i, round := range allPlateSymbolClient {
	// 	fmt.Printf("Round %d:\n", i+1)
	// 	for j, col := range round {
	// 		fmt.Printf("  Col %d: %v\n", j+1, col)
	// 	}
	// }
	var flatPlateSymbolClient [][]int

	for _, round := range allPlateSymbolClient {
		var data []int
		for _, col := range round {
			for _, d := range col {
				data = append(data, d)
			}
		}
		flatPlateSymbolClient = append(flatPlateSymbolClient, data)
	}

	// // 美化打印验证
	// for i, row := range flatPlateSymbolClient {
	// 	fmt.Printf("Row %d: %v\n", i+1, row)
	// }

	os.Remove("data.txt")
	file, err := os.Create("data.txt")
	if err != nil {
		panic(err)
	}

	defer file.Close()

	writer := bufio.NewWriter(file)

	jsonMap := map[string]interface{}{
		"data": flatPlateSymbolClient,
	}
	jsonBytes, _ := json.Marshal(jsonMap)
	writer.WriteString(string(jsonBytes) + "\n")
	writer.Flush()
}
